import type { ImagePreviewInstance } from '@/components/ImagePreview';
import ImagePreview from '@/components/ImagePreview';
import { DeleteOutlined, DragOutlined, EyeOutlined, FileOutlined } from '@ant-design/icons';
import type { DragEndEvent, DragStartEvent } from '@dnd-kit/core';
import {
  closestCenter,
  DndContext,
  DragOverlay,
  KeyboardSensor,
  PointerSensor,
  useDraggable,
  useDroppable,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { Avatar, Button, Card, Col, List, Row, Tag, Tooltip } from 'antd';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useCarInsuranceBulkOcrContext } from '../../context/CarInsuranceBulkOcrContext';
import { getFileNameFromUrl } from '../../utils';
import './index.less';

export interface DragItem {
  id: string;
  name: string;
  type?: 'image' | 'file';
  size?: string;
  url?: string; // 添加文件URL用于预览
}

export interface ListItem {
  id: string;
  vin: string;
  count: number;
  plateNo: string;
}

interface SourceAreaProps {
  items: DragItem[];
}

interface TargetZoneProps {
  id: string;
  title: string;
  items: DragItem[];
  onDeleteItem: (itemId: string) => void;
}

interface DraggableItemProps {
  item: DragItem;
  onDelete?: (itemId: string) => void;
  isDragging?: boolean;
  isSource?: boolean;
}

interface SidebarListProps {
  items: ListItem[];
  selectedId: string | null;
  onSelect: (id: string) => void;
}

// 可拖拽的文件项目组件
const DraggableItem: React.FC<DraggableItemProps> = ({
  item,
  onDelete,
  isDragging = false,
  isSource = false,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging: isCurrentDragging,
  } = useDraggable({
    id: item.id,
  });

  const style = transform
    ? {
      transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
      opacity: isCurrentDragging ? 0.5 : 1,
    }
    : undefined;

  // 处理预览
  const handlePreview = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    console.log('预览按钮点击:', item);

    if (item.url) {
      // 使用全局的预览方法，通过事件或回调传递给父组件
      const event = new CustomEvent('drog-item-preview-file', {
        detail: {
          url: item.url,
          fileName: item.name,
        },
      });
      console.log('发送预览事件:', event.detail);
      window.dispatchEvent(event);
    } else {
      console.warn('文件没有URL，无法预览:', item);
    }
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`draggable-file-item ${isCurrentDragging ? 'dragging' : ''} ${isSource ? 'source-item' : 'target-item'
        }`}
      {...attributes}
      {...listeners}
    >
      <div className="file-item-content">
        {/* <div className="drag-handle" {...attributes} {...listeners}> */}
        <div className="drag-handle">
          <DragOutlined />
        </div>
        <div className="file-icon">
          <Tooltip title="预览">
            <Button
              type="text"
              size="small"
              // icon={item.type === 'image' ? <FileImageOutlined /> : <FolderOutlined />}
              icon={<EyeOutlined />}
              onClick={handlePreview}
              className="preview-btn"
            />
          </Tooltip>
        </div>
        <div className="file-info">
          <div className="file-name">
            <Tooltip title={item.name}>{item.name}</Tooltip>
          </div>
          {/* {item.size && <div className="file-size">{item.size}</div>} */}
        </div>
        <div className="file-actions">
          {onDelete && (
            <Tooltip title="删除">
              <Button
                type="text"
                size="small"
                icon={<DeleteOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  onDelete(item.id);
                }}
                className="delete-btn"
              />
            </Tooltip>
          )}
        </div>
      </div>
    </div>
  );
};

// 源文件区域组件（上方）
const SourceArea: React.FC<SourceAreaProps> = ({ items }) => {
  const { isOver, setNodeRef } = useDroppable({
    id: 'source',
  });

  return (
    <div className={`source-area ${isOver ? 'source-over' : ''}`}>
      <Card
        title={
          <div className="area-header">
            <span>全部文件</span>
            <Tag color="blue">{items.length}</Tag>
          </div>
        }
        size="small"
      >
        <div ref={setNodeRef} className="source-files-container">
          <div className="source-files-grid">
            {items.map((item) => (
              <DraggableItem key={item.id} item={item} isSource={true} />
            ))}
            {items.length === 0 && <div className="empty-source">拖拽文件到此区域</div>}
          </div>
        </div>
      </Card>
    </div>
  );
};

// 目标拖放区域组件（右侧）
const TargetZone: React.FC<TargetZoneProps> = ({ id, title, items, onDeleteItem }) => {
  const { isOver, setNodeRef } = useDroppable({
    id,
  });

  return (
    <div className={`target-zone ${isOver ? 'target-over' : ''}`}>
      <Card
        title={
          <div className="zone-header">
            <span>{title}</span>
            <Tag color="green">{items.length}</Tag>
          </div>
        }
        size="small"
      >
        <div ref={setNodeRef} className="target-drop-area">
          {items.length === 0 ? (
            <div className="empty-target">拖拽文件到此区域</div>
          ) : (
            <div className="target-files-list">
              {items.map((item) => (
                <DraggableItem key={item.id} item={item} onDelete={onDeleteItem} isSource={false} />
              ))}
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

// 侧边栏列表组件（左侧）
const SidebarList: React.FC<SidebarListProps> = ({ items, selectedId, onSelect }) => {
  return (
    <div className="sidebar-list">
      <Card title="已识别车辆数量" size="small">
        <List
          size="small"
          dataSource={items}
          renderItem={(item) => (
            <List.Item
              className={`list-item ${selectedId === item.id ? 'selected' : ''}`}
              onClick={() => onSelect(item.id)}
            >
              <List.Item.Meta
                avatar={<Avatar icon={<FileOutlined />} />}
                title={item.vin}
                description={`${item.plateNo} · ${item.count}个文件`}
              />
            </List.Item>
          )}
        />
      </Card>
    </div>
  );
};

interface DragDropZonesProps {
  onDataChange?: (data: {
    sourceFiles: DragItem[];
    targetZone1: DragItem[];
    targetZone2: DragItem[];
    formattedApiData?: Record<
      string,
      {
        vehicleLicenseOssUrlList: string[];
        insuranceSlipOssUrlList: string[];
      }
    >;
  }) => void;
}

const DragDropZones: React.FC<DragDropZonesProps> = ({ onDataChange }) => {
  // 获取批量OCR上下文数据
  const { batchOcrData } = useCarInsuranceBulkOcrContext();

  // 预览相关
  const previewRef = useRef<ImagePreviewInstance>(null);

  // 将文件URL转换为DragItem格式
  const convertUrlToDragItem = useCallback((url: string, index: number): DragItem => {
    const fileName = getFileNameFromUrl(url);
    return {
      id: `${url}_${index}`, // 使用URL和索引作为唯一ID
      name: fileName,
      type: fileName.toLowerCase().includes('.pdf') ? 'file' : 'image',
      url: url,
    };
  }, []);

  // 根据carInfoDTOList格式化数据
  const formatCarInfoData = useCallback(() => {
    const categoryDataMap: Record<
      string,
      {
        sourceFiles: DragItem[];
        targetZone1: DragItem[];
        targetZone2: DragItem[];
      }
    > = {};

    const listItems: ListItem[] = [];

    // 处理carInfoDTOList中的每个车辆
    batchOcrData.carInfoDTOList?.forEach((carInfo: any) => {
      const vin = carInfo.vin || '无车架号';
      const plateNo = carInfo.plateNo || '无车牌号';

      // 处理车辆证件 (vehicleLicenseOssUrlList -> targetZone1)
      const vehicleDocuments =
        carInfo.vehicleLicenseInfoList?.map((url: string, index: number) =>
          convertUrlToDragItem(url, index),
        ) || [];

      // 处理投保材料 (insuranceSlipOssUrlList -> targetZone2)
      const insuranceMaterials =
        carInfo.insuranceSlipInfoList?.map((url: string, index: number) =>
          convertUrlToDragItem(url, index),
        ) || [];

      // 初始化该VIN的数据结构
      categoryDataMap[carInfo.id] = {
        sourceFiles: [], // 源文件区域暂时为空，可以从notMatchFileDTOList填充
        targetZone1: vehicleDocuments, // 车辆证件
        targetZone2: insuranceMaterials, // 投保材料
      };

      // 添加到列表项
      listItems.push({
        id: carInfo.id,
        vin: vin,
        plateNo: plateNo,
        count: vehicleDocuments.length + insuranceMaterials.length,
      });
    });

    // 处理未匹配的文件作为源文件
    const sourceFiles: DragItem[] = [];
    batchOcrData.notMatchFileDTOList?.forEach((file: any, index: number) => {
      if (file.url) {
        sourceFiles.push(convertUrlToDragItem(file.url, index));
      }
    });

    return { categoryDataMap, listItems, sourceFiles };
  }, [batchOcrData, convertUrlToDragItem]);

  // 获取格式化后的数据
  const {
    categoryDataMap: formattedCategoryDataMap,
    listItems: formattedListItems,
    sourceFiles: formattedSourceFiles,
  } = useMemo(() => {
    return formatCarInfoData();
  }, [formatCarInfoData]);

  // 所有分类的数据状态 - 使用格式化后的数据初始化
  const [categoryDataMap, setCategoryDataMap] = useState<
    Record<
      string,
      {
        sourceFiles: DragItem[];
        targetZone1: DragItem[];
        targetZone2: DragItem[];
      }
    >
  >(formattedCategoryDataMap);

  // 更新categoryDataMap当格式化数据变化时
  useEffect(() => {
    setCategoryDataMap(formattedCategoryDataMap);
  }, [formattedCategoryDataMap]);

  // 分类列表项（使用格式化后的数据）
  const listItems = useMemo(() => {
    return formattedListItems.map((item) => ({
      ...item,
      count:
        (categoryDataMap[item.id]?.sourceFiles.length || 0) +
        (categoryDataMap[item.id]?.targetZone1.length || 0) +
        (categoryDataMap[item.id]?.targetZone2.length || 0),
    }));
  }, [formattedListItems, categoryDataMap]);

  const [selectedListId, setSelectedListId] = useState<string | null>(
    formattedListItems.length > 0 ? formattedListItems[0].id : null,
  );
  const [activeId, setActiveId] = useState<string | null>(null);

  // 格式化数据为API提交格式
  const formatDataForSubmission = useCallback(() => {
    const formattedData: Record<
      string,
      {
        vehicleLicenseOssUrlList: string[];
        insuranceSlipOssUrlList: string[];
      }
    > = {};

    Object.keys(categoryDataMap).forEach((id) => {
      const categoryData = categoryDataMap[id];

      // 提取车辆证件URL (targetZone1)
      const vehicleLicenseOssUrlList = categoryData.targetZone1
        .map((item) => item.url)
        .filter(Boolean) as string[];

      // 提取投保材料URL (targetZone2)
      const insuranceSlipOssUrlList = categoryData.targetZone2
        .map((item) => item.url)
        .filter(Boolean) as string[];

      formattedData[id] = {
        vehicleLicenseOssUrlList,
        insuranceSlipOssUrlList,
      };
    });

    return formattedData;
  }, [categoryDataMap]);

  // 预览事件监听
  useEffect(() => {
    const handlePreviewFile = (event: CustomEvent) => {
      console.log('预览事件触发:', event.detail);
      const { url, fileName } = event.detail;
      if (previewRef.current) {
        previewRef.current.previewFile({
          url,
          fileName,
        });
      } else {
        console.error('预览组件引用不存在');
      }
    };

    window.addEventListener('drog-item-preview-file', handlePreviewFile as EventListener);

    return () => {
      window.removeEventListener('drog-item-preview-file', handlePreviewFile as EventListener);
    };
  }, []);

  // 配置拖拽传感器，避免点击就触发拖拽
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8, // 需要拖拽8px距离才激活
      },
    }),
    useSensor(KeyboardSensor),
  );

  // 获取当前选中分类的数据
  const getCurrentCategoryData = useCallback(() => {
    if (!selectedListId || !categoryDataMap[selectedListId]) {
      return { sourceFiles: [], targetZone1: [], targetZone2: [] };
    }
    return categoryDataMap[selectedListId];
  }, [selectedListId, categoryDataMap]);

  // 获取当前显示的源文件
  const getDisplayedSourceFiles = useCallback(() => {
    return getCurrentCategoryData().sourceFiles;
  }, [getCurrentCategoryData]);

  // 找到项目所在的区域
  const findItemLocation = useCallback(
    (id: string) => {
      const currentData = getCurrentCategoryData();

      if (currentData.sourceFiles.find((item) => item.id === id)) {
        return 'source';
      }

      if (currentData.targetZone1.find((item) => item.id === id)) {
        return 'targetZone1';
      }

      if (currentData.targetZone2.find((item) => item.id === id)) {
        return 'targetZone2';
      }

      return null;
    },
    [getCurrentCategoryData],
  );

  // 获取拖拽的项目
  const getActiveItem = useCallback(() => {
    if (!activeId) return null;

    const currentData = getCurrentCategoryData();

    // 从源文件中查找
    const sourceItem = currentData.sourceFiles.find((item) => item.id === activeId);
    if (sourceItem) return sourceItem;

    // 从目标区域中查找
    const targetItem1 = currentData.targetZone1.find((item) => item.id === activeId);
    if (targetItem1) return targetItem1;

    const targetItem2 = currentData.targetZone2.find((item) => item.id === activeId);
    if (targetItem2) return targetItem2;

    return null;
  }, [activeId, getCurrentCategoryData]);

  // 开始拖拽
  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  // 结束拖拽
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    setActiveId(null);

    if (!over || !selectedListId) {
      return;
    }

    const c_activeId = active.id as string;
    const overId = over.id as string;

    // 如果拖拽到同一个位置，不做任何操作
    const sourceLocation = findItemLocation(c_activeId);
    if (sourceLocation === overId) {
      return;
    }

    const currentData = getCurrentCategoryData();
    let draggedItem: DragItem | undefined;

    // 找到被拖拽的项目
    if (sourceLocation === 'source') {
      draggedItem = currentData.sourceFiles.find((item) => item.id === c_activeId);
    } else if (sourceLocation === 'targetZone1') {
      draggedItem = currentData.targetZone1.find((item) => item.id === c_activeId);
    } else if (sourceLocation === 'targetZone2') {
      draggedItem = currentData.targetZone2.find((item) => item.id === c_activeId);
    }

    // 只有在找到项目且目标区域有效时才进行移动
    if (
      draggedItem &&
      (overId === 'source' || overId === 'targetZone1' || overId === 'targetZone2')
    ) {
      setCategoryDataMap((prev) => {
        const newData = { ...prev[selectedListId] };

        // 从原位置移除
        if (sourceLocation === 'source') {
          newData.sourceFiles = newData.sourceFiles.filter((item) => item.id !== c_activeId);
        } else if (sourceLocation === 'targetZone1') {
          newData.targetZone1 = newData.targetZone1.filter((item) => item.id !== c_activeId);
        } else if (sourceLocation === 'targetZone2') {
          newData.targetZone2 = newData.targetZone2.filter((item) => item.id !== c_activeId);
        }

        // 添加到新位置
        if (overId === 'source') {
          newData.sourceFiles = [...newData.sourceFiles, draggedItem];
        } else if (overId === 'targetZone1') {
          newData.targetZone1 = [...newData.targetZone1, draggedItem];
        } else if (overId === 'targetZone2') {
          newData.targetZone2 = [...newData.targetZone2, draggedItem];
        }

        const updatedData = {
          ...prev,
          [selectedListId]: newData,
        };

        // 触发数据变更回调，包含格式化后的API数据
        const formattedApiData = formatDataForSubmission();
        onDataChange?.({
          sourceFiles: newData.sourceFiles,
          targetZone1: newData.targetZone1,
          targetZone2: newData.targetZone2,
          formattedApiData, // 添加格式化后的API数据
        });

        return updatedData;
      });
    }
  };

  // 删除目标区域中的项目
  const handleDeleteItem = useCallback(
    (itemId: string) => {
      if (!selectedListId) return;

      setCategoryDataMap((prev) => {
        const currentData = prev[selectedListId];
        let updatedData = { ...currentData };

        // 检查是否在目标区域1中
        if (currentData.targetZone1.find((item) => item.id === itemId)) {
          updatedData = {
            ...updatedData,
            targetZone1: currentData.targetZone1.filter((item) => item.id !== itemId),
          };
        }
        // 检查是否在目标区域2中
        else if (currentData.targetZone2.find((item) => item.id === itemId)) {
          updatedData = {
            ...updatedData,
            targetZone2: currentData.targetZone2.filter((item) => item.id !== itemId),
          };
        }

        const newCategoryDataMap = {
          ...prev,
          [selectedListId]: updatedData,
        };

        // 触发数据变更回调，包含格式化后的API数据
        const formattedApiData = formatDataForSubmission();
        onDataChange?.({
          sourceFiles: updatedData.sourceFiles,
          targetZone1: updatedData.targetZone1,
          targetZone2: updatedData.targetZone2,
          formattedApiData, // 添加格式化后的API数据
        });

        return newCategoryDataMap;
      });
    },
    [selectedListId, onDataChange],
  );

  return (
    <div className="drag-drop-zones-new">
      <DndContext
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        sensors={sensors}
      >
        {/* 上方：源文件区域 */}
        <div className="top-section">
          <SourceArea items={getDisplayedSourceFiles()} />
        </div>

        {/* 下方：左侧列表 + 右侧目标区域 */}
        <div className="bottom-section">
          <Row gutter={16}>
            {/* 左侧：分类列表 */}
            <Col span={8}>
              <SidebarList
                items={listItems}
                selectedId={selectedListId}
                onSelect={setSelectedListId}
              />
            </Col>

            {/* 右侧：两个目标区域 */}
            <Col span={16}>
              <div className="target-zones">
                <div className="target-zones-header">
                  <span>当前车架号：</span>
                  {selectedListId && (
                    <Tag color="orange">
                      {listItems.find((item) => item.id === selectedListId)?.vin}
                    </Tag>
                  )}
                </div>
                <Row gutter={16}>
                  <Col span={12}>
                    <TargetZone
                      id="targetZone1"
                      title="车辆证件"
                      items={getCurrentCategoryData().targetZone1}
                      onDeleteItem={handleDeleteItem}
                    />
                  </Col>
                  <Col span={12}>
                    <TargetZone
                      id="targetZone2"
                      title="投保材料"
                      items={getCurrentCategoryData().targetZone2}
                      onDeleteItem={handleDeleteItem}
                    />
                  </Col>
                </Row>
              </div>
            </Col>
          </Row>
        </div>

        {/* 拖拽预览层 */}
        <DragOverlay>
          {activeId ? (
            <div className="drag-overlay-new">
              <DraggableItem item={getActiveItem()!} isDragging />
            </div>
          ) : null}
        </DragOverlay>
      </DndContext>

      {/* 文件预览组件 - 不占用布局空间 */}
      <ImagePreview ref={previewRef} />
    </div>
  );
};

export default DragDropZones;
