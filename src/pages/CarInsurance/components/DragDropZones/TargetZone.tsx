import React from 'react';
import { useDroppable } from "@dnd-kit/core";
import { Card, Tag } from "antd";
import DraggableItem from "./DraggableItem";
import { DragItem } from "../DragDropZones";

interface TargetZoneProps {
    id: string;
    title: string;
    items: DragItem[];
    onDeleteItem: (itemId: string) => void;
}

// 目标拖放区域组件（右侧）
const TargetZone: React.FC<TargetZoneProps> = ({ id, title, items, onDeleteItem }) => {
    const { isOver, setNodeRef } = useDroppable({
        id,
    });

    return (
        <div className={`target-zone ${isOver ? 'target-over' : ''}`}>
            <Card
                title={
                    <div className="zone-header">
                        <span>{title}</span>
                        <Tag color="green">{items.length}</Tag>
                    </div>
                }
                size="small"
            >
                <div ref={setNodeRef} className="target-drop-area">
                    {items.length === 0 ? (
                        <div className="empty-target">拖拽文件到此区域</div>
                    ) : (
                        <div className="target-files-list">
                            {items.map((item) => (
                                <DraggableItem key={item.id} item={item} onDelete={onDeleteItem} isSource={false} />
                            ))}
                        </div>
                    )}
                </div>
            </Card>
        </div>
    );
};


export default React.memo(TargetZone);
