import React from 'react';
import { Avatar, Card, List } from 'antd';
import { FileOutlined } from '@ant-design/icons';
import { ListItem } from '../DragDropZones';

interface SidebarListProps {
    items: ListItem[];
    selectedId: string | null;
    onSelect: (id: string) => void;
}

// 侧边栏列表组件（左侧）
const SidebarList: React.FC<SidebarListProps> = ({ items, selectedId, onSelect }) => {
    return (
        <div className="sidebar-list">
            <Card title="已识别车辆数量" size="small">
                <List
                    size="small"
                    dataSource={items}
                    renderItem={(item) => (
                        <List.Item
                            className={`list-item ${selectedId === item.id ? 'selected' : ''}`}
                            onClick={() => onSelect(item.id)}
                        >
                            <List.Item.Meta
                                avatar={<Avatar icon={<FileOutlined />} />}
                                title={item.vin}
                                description={`${item.plateNo} · ${item.count}个文件`}
                            />
                        </List.Item>
                    )}
                />
            </Card>
        </div>
    );
};

export default React.memo(SidebarList);
