import { Button, Popconfirm, Progress, Space, Spin, Typography } from 'antd';
import dayjs from 'dayjs';
import React, { memo } from 'react';
import { useCarInsuranceBulkOcrContext } from '../context/CarInsuranceBulkOcrContext';
import { BULK_UPLOAD_EVENT_ACTION } from '../type';
import './index.less';

const { Text, Title } = Typography;
interface BatchOperationStatusProps {}

const BatchOperationStatus: React.FC<BatchOperationStatusProps> = () => {
  const {
    batchOperationStatus,
    checkMatchProcessCallback,
    handleGiveUpOrReAction,
    batchAddModalRef,
  } = useCarInsuranceBulkOcrContext();

  // 批量操作进行中的UI
  if (batchOperationStatus.isProcessing && !batchOperationStatus.isMatching) {
    return (
      <div className="batch-operation-status-container">
        <Spin size="large" />
        <Title level={4} style={{ marginTop: '24px', marginBottom: '8px', color: '#1890ff' }}>
          批量新增中
        </Title>
        <Text
          type="secondary"
          style={{ fontSize: '14px', textAlign: 'center', marginBottom: '12px' }}
        >
          批量操作进行中，请
          <b>查看详情</b>
          继续完成任务...
        </Text>
        <Space>
          <Popconfirm
            onConfirm={() => {
              handleGiveUpOrReAction?.(BULK_UPLOAD_EVENT_ACTION.GIVE_UP_MATCH);
            }}
            title="是否放弃本次操作？"
            okText="确认"
            cancelText="取消"
          >
            <Button danger>放弃本次操作</Button>
          </Popconfirm>
          <Button
            type="primary"
            onClick={() => {
              batchAddModalRef?.current?.show();
            }}
          >
            查看详情
          </Button>
        </Space>
      </div>
    );
  }

  // 匹配中后台任务异常中断时展示
  if (batchOperationStatus.isMatching && batchOperationStatus.matchInProcessTerminated) {
    return (
      <div className="batch-operation-status-container">
        <div style={{ width: '100%', maxWidth: '400px', textAlign: 'center' }}>
          <Title level={4} style={{ marginBottom: '24px', color: '#1890ff' }}>
            数据匹配中任务中断
          </Title>

          <Progress
            percent={batchOperationStatus.matchProgress}
            status="active"
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
            style={{ marginBottom: '16px' }}
          />

          <Space>
            <Button
              danger
              onClick={() => {
                handleGiveUpOrReAction?.(BULK_UPLOAD_EVENT_ACTION.TERMINATED_MATCH);
              }}
              disabled={!handleGiveUpOrReAction}
            >
              放弃匹配
            </Button>
            <Button
              type="primary"
              onClick={() => {
                handleGiveUpOrReAction?.(BULK_UPLOAD_EVENT_ACTION.CONTINUE_MATCH);
              }}
              disabled={!handleGiveUpOrReAction}
            >
              继续开始
            </Button>
          </Space>
        </div>
      </div>
    );
  }

  // 数据匹配中的UI
  if (batchOperationStatus.isMatching && !batchOperationStatus.matchInProcessTerminated) {
    return (
      <div className="batch-operation-status-container">
        <div style={{ width: '100%', maxWidth: '400px', textAlign: 'center' }}>
          <Title level={4} style={{ marginBottom: '24px', color: '#1890ff' }}>
            数据匹配中：{batchOperationStatus.matchProgress}%
          </Title>

          <Progress
            percent={batchOperationStatus.matchProgress}
            status="active"
            strokeColor={{
              '0%': '#108ee9',
              '100%': '#87d068',
            }}
            style={{ marginBottom: '16px' }}
          />

          <Space>
            <Button
              danger
              onClick={() => {
                handleGiveUpOrReAction?.(BULK_UPLOAD_EVENT_ACTION.TERMINATED_MATCH);
              }}
              disabled={!handleGiveUpOrReAction}
            >
              放弃匹配
            </Button>
            <Button
              type="primary"
              onClick={() => {
                checkMatchProcessCallback?.();
              }}
              disabled={!checkMatchProcessCallback}
            >
              刷新
            </Button>
          </Space>
        </div>
      </div>
    );
  }

  // 匹配完成的UI
  if (batchOperationStatus.isMatchComplete) {
    return (
      <div className="batch-operation-status-container">
        <div style={{ width: '100%', maxWidth: '400px', textAlign: 'center' }}>
          <Title level={4} style={{ marginBottom: '24px', color: '#52c41a' }}>
            数据匹配完成
          </Title>

          {/* <div style={{ marginBottom: '24px' }}>
            <Text style={{ fontSize: '16px', color: '#333' }}>
              匹配结果：{batchOperationStatus.matchedTotalCount} / {batchOperationStatus.totalCount}{' '}
              条数据
            </Text>
          </div> */}

          <div style={{ marginBottom: '24px' }}>
            <Text
              type="secondary"
              style={{ fontSize: '12px', marginBottom: '24px', display: 'block' }}
            >
              匹配耗时：
              {dayjs(batchOperationStatus.endTime).diff(
                dayjs(batchOperationStatus.startTime),
                'second',
              )}{' '}
              秒
            </Text>
          </div>

          <Space>
            <Button
              type="primary"
              onClick={() => {
                batchAddModalRef?.current?.show();
              }}
            >
              查看匹配结果
            </Button>
          </Space>
        </div>
      </div>
    );
  }

  return null;
};

export default memo(BatchOperationStatus);
