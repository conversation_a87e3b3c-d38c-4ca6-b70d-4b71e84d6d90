import HeaderTab from '@/components/HeaderTab';
import { LENDING_MODEL_ENUM } from '@/enums';
import { EcarInsuranceOperate, EcarInsuranceStatus } from '@/utils/bankend/enum';
import { PageContainer } from '@ant-design/pro-layout';
import { history, useAccess, useModel } from '@umijs/max';
import { useRequest } from 'ahooks';
import type { FormInstance, UploadFile } from 'antd';
import {
  Alert,
  Button,
  Card,
  DatePicker,
  Descriptions,
  Divider,
  Form,
  Input,
  message,
  Modal,
  Spin,
  Table,
  Tag,
} from 'antd';
import dayjs from 'dayjs';
import html2canvas from 'html2canvas';
import { QRCodeCanvas } from 'qrcode.react';
import React, { memo, useCallback, useEffect, useRef, useState } from 'react';
import AppendInfo from './components/AppendInfo';
import ApproveCard from './components/ApproveCard';
import ApproveInfo from './components/ApproveInfo';
import BaseInfo from './components/BaseInfo';
import CarInfo from './components/CarInfo';
import CompanyInfo from './components/CompanyInfo';
import ConfirmButton from './components/ConfirmButton';
import DownPaymentsVoucherInfo from './components/DownPaymentsVoucherInfo';
import EnterpriseInfo from './components/EnterpriseInfo';
import LoanInfo from './components/LoanInfo';
import LoanPersonal from './components/LoanPersonal';
import OperationRecords from './components/OperationRecords';
import OtherInfo from './components/OtherInfo';
import ProductInfo from './components/ProductInfo';
import Remark from './components/Remark';
import UploadMultipleImg from './components/UploadMultipleImg';
import { CarInsuranceBulkOcrProvider } from './context/CarInsuranceBulkOcrContext';

import {
  cancelClaim,
  checkCarPolicyOrderStatus,
  getChannelInfo,
  getOverdueCaseInfo,
  monthlyCalcu,
  saveCarInsurance,
  updateCarInspected,
} from './services';
import './styles/index.css';
import type { IrefData, IsubmitData, UcarInsuranceOperate } from './type';
import {
  ChannelUserOperateMap,
  CompanyChannelUserOperateMap,
  CompanyOperateMap,
  ECARINSURANCE_OPERATE_ACTION,
  needPollingOrderStatus,
  OperateMap,
  PersonalChannelUserOperateMap,
  PersonalOperateMap,
} from './type';
import { underlineToHump } from './utils';
const { TextArea } = Input;

function CarInsuranceEdit() {
  const screenshotContainer = useRef<HTMLDivElement>(null);
  const [modal, contextHolder] = Modal.useModal();
  const [baseInfoForm] = Form.useForm();
  const [productInfoForm] = Form.useForm();
  const [loanPersonalInfoForm] = Form.useForm();
  const [enterpriseInfoForm] = Form.useForm();
  const [companyInfoForm] = Form.useForm();
  const [paymentInfoForm] = Form.useForm();
  const [otherInfoForm] = Form.useForm<{
    otherInformationList: UploadFile[];
  }>();
  const [remarkForm] = Form.useForm();
  const {
    getDetail,
    carInfo,
    detailLoading,
    detailData,
    setDetailData,
    setCarInfo,
    setIsEditable,
    borrowerType,
    setBorrowerType,
    setChannelList,
  } = useModel('CarInsurance.carInsurance');
  const { orderNo, multiplexOrderNo } = (history as any).location.query;
  const { initialState = {} } = useModel<any>('@@initialState');
  const { userId, userName, channelCode, channelLevel, pid } = initialState?.currentUser;
  const access = useAccess();
  // TODO:写入 驳回和通过信息
  const refData = useRef<IrefData>({ rejectMsg: '', channelIsOnChange: false, remarkMsg: '' });
  // 是否是个人领取的订单
  const isOwnerOrder = pid === detailData?.operatorUserId;
  // 操作按钮的 loading 有些按钮不需要loading 永远都是false
  const [btnLoading, setbtnLoading] = useState<any>({
    submitForReviewLoading: false, // 提交审核的loading
  });
  // 匹配状态
  const [ocrMatchResult, setOcrMatchResult] = useState<any>({});

  function clearFormData() {
    // 新增 假如先编辑 再新增 就会有全局状态的缓存 所以要清空详情数据
    setDetailData({} as any);
    setCarInfo([]);
    setIsEditable(true);
    baseInfoForm.resetFields();
    productInfoForm.resetFields();
    enterpriseInfoForm.resetFields();
    companyInfoForm.resetFields();
    paymentInfoForm.resetFields();
    setBorrowerType(undefined);
    loanPersonalInfoForm.resetFields();
  }

  const getDetailData = useCallback(async () => {
    // 状态
    // 复用的时候 刷新之后也应该要能展示复用的信息
    if (orderNo || multiplexOrderNo) {
      await getDetail(orderNo || multiplexOrderNo);
      // 复用 编辑
    }
    // 保存草稿后orderNo发生了变化 重新获取数据
  }, [orderNo, multiplexOrderNo]);
  /**
   * 获取表单数据,并将数据组装成最终提交的样子
   */
  function getSubmitData(behavior: UcarInsuranceOperate): IsubmitData {
    const { orderNo: orderNo1 } = (history as any).location.query;
    const baseInfo = baseInfoForm.getFieldsValue();
    const productInfo = productInfoForm.getFieldsValue();
    const insuranceInfo = companyInfoForm.getFieldsValue();
    const enterpriseInfo = enterpriseInfoForm.getFieldsValue();
    const paymentInfo = paymentInfoForm.getFieldsValue();
    const otherInfo = otherInfoForm.getFieldsValue();
    const loanPersonalInfo = loanPersonalInfoForm.getFieldsValue();
    const remarkInfo = remarkForm.getFieldsValue();

    // 如果保证金和手续费没有配置 给默认值0
    if (!productInfo?.bait && productInfo?.bait !== 0) {
      productInfo.bait = 0;
      productInfo.baitType = 2;
    }

    if (!productInfo?.commission && productInfo?.commission !== 0) {
      productInfo.commission = 0;
      productInfo.commissionType = 2;
    }

    let downPaymentsDTO: any = {};
    if (
      detailData?.orderStatus === EcarInsuranceStatus.TO_BE_DOWN_PAYMENT ||
      detailData?.orderStatus === EcarInsuranceStatus.TO_BE_DOWN_PAYMENT_REJECT
    ) {
      downPaymentsDTO = {
        ...paymentInfo,
        paymentDate: paymentInfo?.paymentDate?.format('YYYY-MM-DD'),
      };
    } else {
      downPaymentsDTO = detailData.downPaymentsDTO || {};
    }

    const params = {
      // 首次保存草稿的时候才去传
      createdBy: !orderNo ? userId : undefined,
      approvalOperator: userName,
      baseInfo,
      productInfo,
      personalInfo: loanPersonalInfo,
      insuranceInfo,
      enterpriseInfo: {
        ...enterpriseInfo,
        stakeholderId: detailData?.enterpriseInfo?.stakeholderId,
      },
      userBehaviorEnum: behavior,
      carInfoDTOList: carInfo,
      orderNo: orderNo1,
      downPaymentsDTO,
      otherInformationList: otherInfo?.otherInformationList?.map((item) => {
        const { name } = item;
        return {
          fileName: name,
          // netWorkPath: netWorkPath,
          shortOssUrl: (item as any)?.shortOssUrl,
        };
      }),
      rejectMsg: refData.current.rejectMsg,
      remarkMsg: refData.current.remarkMsg,
      ...remarkInfo,
    };
    return params;
  }

  function handleClosed() {
    history.push('/businessMng/car-insurance/list');
  }

  // 校验特定字段的方法
  const validateFields = (form: FormInstance, fields: string | string[]) => {
    return new Promise(async (res, rej) => {
      try {
        if (Array.isArray(fields)) {
          await form.validateFields([...fields.filter(Boolean)]);
        } else {
          await form.validateFields([fields]);
        }
        // 如果校验通过，values将包含校验字段的值
        res('校验通过');
      } catch (errorInfo) {
        // 只要有失败就返回失败 返回失败之后 剩余的微任务如何阻止?
        scrollToFirstError(form, errorInfo);
        // 如果校验失败，errorInfo将包含校验失败的字段和错误信息
        rej('校验失败');
      }
    });
  };

  // 最终提交
  async function doAction(
    action: UcarInsuranceOperate,
    options?: { skipGlobalErrorTip?: boolean; noLoading?: boolean },
  ) {
    // CHECK_VEHICLE 检查车辆有点特殊 是在提交审核时完成的
    const humpOperate = underlineToHump(
      action === ECARINSURANCE_OPERATE_ACTION.CHECK_VEHICLE
        ? ECARINSURANCE_OPERATE_ACTION.SUBMIT_FOR_REVIEW
        : action,
    );
    if (action === ECARINSURANCE_OPERATE_ACTION.SUBMIT_PASS) {
      //  审核通过需要检查一下投保主体和合并打款是否选了，其他状态不校验
      await validateFields(companyInfoForm, ['insuranceSubject', 'mergePayFlag']);
      // await validateFields(companyInfoForm, 'mergePayFlag');
    }
    try {
      if (!options?.noLoading) {
        setbtnLoading({ [humpOperate]: true });
      }
      const formData = getSubmitData(action);
      console.log('formData', formData);
      const data = await saveCarInsurance(formData, options);
      if (action === ECARINSURANCE_OPERATE_ACTION.CHECK_VEHICLE) {
        // 校验车架号 不需要走后续逻辑
        return;
      }
      if (!options?.noLoading) {
        message.success('操作成功');
      }
      if (!orderNo && data) {
        // 假如还未 保存草稿
        history.push(`/businessMng/car-insurance/detail?orderNo=${data}`);
      }

      // 编辑的时候如果是删除草稿跳转列表页面 不需要获取详情 其他情况都需要获取详情
      if (
        action === ECARINSURANCE_OPERATE_ACTION.DELETE_DRAFT ||
        action === ECARINSURANCE_OPERATE_ACTION.UPLOAD_DOWN_CHECK_PASS
      ) {
        return history.push(`/businessMng/car-insurance/list`);
      } else {
        await getDetail(data, { noLoading: options?.noLoading });
      }
      return data;
    } catch (error) {
      // 为了让外面的catch 捕获错误
      return Promise.reject(error);
    } finally {
      setbtnLoading({ [humpOperate]: false });
    }
  }

  /**
   * 滚动有错误的位置
   * @param form form实例
   * @param err form表单校验错误时的信息
   */
  function scrollToFirstError(form: FormInstance, err: any) {
    const { errorFields = [] } = err;
    const { name = [] } = errorFields[0];
    const firstErrorName = name[0];
    // 不知道为啥 方法手动校验 无法触发scrollToFirstError属性
    form.scrollToField(firstErrorName, {
      block: 'center', // 滚动位置
      behavior: 'smooth', // 平滑滚动
    });
  }

  // 校验所有的form表单信息
  async function verifyAllFormInfo() {
    const forms = [
      baseInfoForm,
      loanPersonalInfoForm,
      productInfoForm,
      enterpriseInfoForm,
      companyInfoForm,
    ];
    // promise.all 在错误信息中不好拿对应的form实例

    return new Promise((res, rej) => {
      const success = [];
      for (let i = 0; i < forms.length; i++) {
        const form = forms[i];
        form
          .validateFields()
          .then(() => {
            // if(i === arr.length - 1){
            // 如果比如companyInfoForm成功的校验比较快 ， 块级作用于找到i已经是最后了
            // 这里i得值与forms中promise的顺序有关
            //   res("所有都校验成功")
            // }
            success.push(1);
            if (success.length === forms.length) {
              // 没有错误 走这里
              res('所有都校验成功');
            } else {
              // 不能rej 这里
            }
          })
          .catch((err) => {
            // 有错误
            rej(err);
            // 只要有失败就返回失败 返回失败之后 剩余的微任务如何阻止?
            scrollToFirstError(form, err);
          });
      }
    });
  }

  // 校验重复车架号
  async function verifyRepeatVin() {
    return new Promise((res, rej) => {
      const existCarInfo = carInfo.filter((car) => !car.deleteFlag);
      if ([...new Set(existCarInfo.map((item) => item.vin))].length !== existCarInfo?.length) {
        message.error('存在重复车架号');
        rej('存在重复车架号');
      } else {
        res('ok');
      }
    });
  }

  // 校验所有的车辆信息
  async function verifyCarInfo() {
    return new Promise((res, rej) => {
      if (!carInfo?.length) {
        message.error('车辆信息不能为空');
        rej('车辆信息不能为空');
      } else {
        // 已被标记的车辆不应该被校验
        if (carInfo.filter((item) => !item.deleteFlag).filter((item) => item.error).length) {
          message.error('车辆信息有异常,请完善车辆信息');
          rej('车辆信息有误');
        } else {
          res('ok');
        }
      }
    });
  }
  // 校验产品信息
  async function verifyProductInfoForm() {
    return new Promise((res, rej) => {
      productInfoForm
        .validateFields()
        .then(() => {
          res(1);
        })
        .catch((err) => {
          message.error('请先完善产品信息');
          scrollToFirstError(productInfoForm, err);
          rej(err);
        });
    });
  }

  async function checkVehicle() {
    return new Promise((res, rej) => {
      // skipErrorHandler 跳过全局提示的错误,采用catch自定义错误
      doAction(ECARINSURANCE_OPERATE_ACTION.CHECK_VEHICLE, { skipGlobalErrorTip: true })
        .then(() => {
          res('ok');
        })
        .catch((err) => {
          rej(err);
          if (err) {
            Modal.confirm({
              icon: null,
              content: err?.msg,
              okText: '继续提交',
              onOk() {
                doAction(ECARINSURANCE_OPERATE_ACTION.SUBMIT_FOR_REVIEW);
              },
            });
          }
        });
    });
  }

  // 检查订单是否被取消
  const { run, cancel } = useRequest(
    async () => {
      try {
        if (!orderNo) return;
        const res = await checkCarPolicyOrderStatus({ orderNo: orderNo });
        // data: { canceled: boolean, currentOrderStatus: number }
        if (res?.success && !!res?.data?.canceled) {
          // 取消轮询
          cancel();
          // 先销毁旧的
          Modal.destroyAll();
          // 重新提示
          Modal.confirm({
            title: '通知',
            content: '该笔订单已取消申请',
            centered: true,
            closeIcon: null,
            maskClosable: false,
            keyboard: false,
            cancelButtonProps: {
              style: {
                display: 'none',
              },
            },
            onOk() {
              Modal.destroyAll();
              // 重新获取数据
              getDetailData();
            },
          });
        }
      } catch (error) {
        return Promise.reject(error);
      }
    },
    {
      pollingInterval: 10000, // 轮询间隔
      ready: !!orderNo, // 订单号存在时才轮询
      debounceInterval: 1000, // 防抖，防止模块短时间内多次请求
      manual: true, // 初始化受控
    },
  );

  // 开始轮询检查订单状态
  useEffect(() => {
    if (needPollingOrderStatus.includes(detailData.orderStatus)) {
      run();
    }
    return () => {
      cancel();
    };
  }, [run, cancel, detailData.orderStatus]);

  // 获取详情
  useEffect(() => {
    getDetailData();
  }, []);

  // 获取渠道信息
  useEffect(() => {
    getChannelInfo({ channelCode, channelLevel }).then(setChannelList); // 获取所有的渠道列表
    return () => {
      // 新增
      clearFormData();
    };
  }, []);

  // 提交审核
  async function handleSunmitForReview() {
    // 校验所有错误信息 按照顺序校验 基本 baseInfo productInfo  enterpriseInfo insuranceInfo

    try {
      // 校验所有的表单信息
      await verifyAllFormInfo();
      // 校验车辆信息
      await verifyCarInfo();
      // 校验首付
      await verifyDownPayment();

      // 校验重复车架号
      await verifyRepeatVin();

      // 后端校验重复车架号有重复的也可以继续提交
      // await checkVehicle(); // 2025-02-10 移除此次校验弹窗提示
      // 提交审核
      await doAction(ECARINSURANCE_OPERATE_ACTION.SUBMIT_FOR_REVIEW);
    } catch (error) {
      console.log('error', error);
    }
  }

  async function verifyDownPayment() {
    const { downPayment, downPaymentType } = productInfoForm.getFieldsValue();
    if (downPaymentType === 2) {
      // 按照金额 固定金额 或者 金额范围
      const commercialInsurancePremium = carInfo.reduce((pre, cur) => {
        return pre + Number(cur.commercialInsurancePremium);
      }, 0);
      const otherPaymentAmount1 = carInfo.reduce((pre, cur) => {
        const { compulsoryInsurancePremium, otherPaymentAmount, vehicleTaxAmount } = cur;
        return (
          pre +
          Number(compulsoryInsurancePremium) +
          Number(otherPaymentAmount) +
          Number(vehicleTaxAmount)
        );
      }, 0);
      const sumInsurancePremium = commercialInsurancePremium + otherPaymentAmount1;
      if (downPayment > sumInsurancePremium) {
        message.error('首付不能大于总保费');
        return Promise.reject('首付不能大于总保费');
      }
    }
    return Promise.resolve('校验通过');
  }

  /**
   * 月供测算
   */
  async function handleMonthlyCalcu() {
    const humpOperate = underlineToHump(ECARINSURANCE_OPERATE_ACTION.MONTHLY_CALCU);
    try {
      setbtnLoading({ [humpOperate]: true });
      // 校验产品信息
      await verifyProductInfoForm();

      // 校验车辆信息
      await verifyCarInfo();

      // 校验首付
      await verifyDownPayment();

      // 校验重复车架后
      await verifyRepeatVin();
      const businessInsuranceAmount = carInfo.reduce(
        (pre, cur) => pre + Number(cur.commercialInsurancePremium),
        0,
      );
      const productInfo = productInfoForm.getFieldsValue();
      const loanPersonalName = loanPersonalInfoForm.getFieldValue('name');
      const enterpriseName = enterpriseInfoForm.getFieldValue('enterpriseName');
      const {
        downPaymentType,
        downPayment,
        productCode,
        term: totalTerm,
        productName,
        bait,
        baitType,
        commission,
        commissionType,
      } = productInfo;
      const params = {
        businessInsuranceAmount,
        carInfoDTOList: carInfo,
        totalTerm,
        downPaymentType,
        downPayment,
        productCode,
        bait,
        baitType,
        commission,
        commissionType,
      };
      const data = await monthlyCalcu(params);
      const columns1 = [
        { label: '借款总金额(元)', value: 'loanAmount' },
        {
          label: '首付金额(元)',
          value: 'realDownPaymentAmount',
          render: (text: string) => {
            return <span style={{ color: 'red', fontWeight: 'bolder' }}>{text}</span>;
          },
        },
        { label: '借款期限', value: 'totalTerm' },
        { label: '利息总额(元)', value: 'interestAmount' },
      ];
      const columns2 = [
        { title: '分期', dataIndex: 'term' },
        { title: '还款日', dataIndex: 'repayDate' },
        { title: '还款金额(元)', dataIndex: 'amount' },
        { title: '还款本金(元)', dataIndex: 'principal' },
        { title: '还款利息(元)', dataIndex: 'interest' },
      ];

      const columns3 = [
        { title: '车架号', dataIndex: 'vin' },
        { title: '车牌号', dataIndex: 'plateNo' },
      ];

      function handleImgScreenshot() {
        const element = screenshotContainer.current; // 要转换成图片的DOM元素
        if (element) {
          const width = element.offsetWidth; //   宽度
          const height = element.offsetHeight; //   高度
          html2canvas(element, {
            width,
            height,
            // scale: 1, //设置放大的倍数
            useCORS: true, // 如果页面中有跨域图片，确保设置为true
          })
            .then(async (canvas) => {
              const imgbase64 = canvas.toDataURL('image/png'); // 转换为DataURL格式的图片
              const link = document.createElement('a');
              link.href = imgbase64;
              link.download = `月供测算-${dayjs().format('YYYY-MM-DD')}.png`;
              link.click();
              const img1 = new Image();
              img1.src = imgbase64;

              const data = await fetch(imgbase64);
              const blob = await data.blob();

              navigator.clipboard
                .write([
                  new ClipboardItem({
                    [blob.type]: blob as any,
                  }),
                ])
                .then(function () {
                  message.success('复制成功');
                })
                .catch(function (error) {
                  message.error('未知的错误');
                  console.error('未知的错误', error);
                });
            })
            .catch(() => {});
        }
      }
      // let platform = 'crtl';
      // if (navigator.userAgent.indexOf('Mac')) {
      //   platform = 'command';
      // }
      Modal.success({
        title: (
          <div>
            月供测算
            <Button
              style={{ marginLeft: 12 }}
              onClick={() => {
                handleImgScreenshot();
              }}
            >
              下载图片
            </Button>
          </div>
        ),
        width: 1200,
        content: (
          <div ref={screenshotContainer}>
            <Alert
              message={`测算时间:${dayjs().format(
                'YYYY-MM-DD',
              )};方案仅供参考,具体价格按照系统实际还款计划为准`}
              type="error"
            />
            <div style={{ display: 'flex', marginTop: 12 }}>
              <div style={{ width: '50%' }}>
                <Descriptions title="" column={2}>
                  <Descriptions.Item label="金融方案">{productName}</Descriptions.Item>
                  <Descriptions.Item label="借款客户">
                    {borrowerType === 'COMPANY' ? enterpriseName : loanPersonalName}
                  </Descriptions.Item>
                  {columns1.map((item) => {
                    const { label, value, render } = item;
                    return (
                      <Descriptions.Item label={label} key={label}>
                        {render ? render(data[value]) : data[value]}
                      </Descriptions.Item>
                    );
                  })}
                </Descriptions>
                <Table
                  title={() => '车辆信息:' + carInfo?.length + '辆'}
                  columns={columns3}
                  dataSource={carInfo}
                  size="small"
                  pagination={false}
                />
              </div>
              <div>
                <Table
                  title={() => '还款计划表'}
                  columns={columns2}
                  dataSource={data.repayPlanList}
                  size="small"
                  pagination={false}
                />
              </div>
            </div>
          </div>
        ),
      });
    } catch (error) {
    } finally {
      setbtnLoading({ [humpOperate]: false });
    }
  }

  /**
   * 签约/首付二维码
   */
  function handleSignOrDownPaymentsQrCode(type: UcarInsuranceOperate) {
    const {
      shortUrlInfo: { longUrl, phone } = {},
      enterpriseInfo: { authorizerName },
    } = detailData;
    if (!longUrl) {
      return message.error(`${EcarInsuranceOperate[type]}链接不存在`);
    }
    Modal.info({
      title: `${EcarInsuranceOperate[type]}`, // 签约二维码or首付二维码
      closable: true,
      maskClosable: true,
      icon: null,
      content: (
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 20,
            textAlign: 'center',
          }}
        >
          <QRCodeCanvas value={`${longUrl}`} />
          {/* 企业的待首付无 【签约或首付二维码】按钮 */}
          {borrowerType === 'COMPANY' && (
            <>
              <div>
                <div>
                  仅限用于: {authorizerName}(手机号尾号
                  {phone?.substring(phone?.length - 4)})签约
                </div>
                <div style={{ color: 'red' }}>其他客户使用会签约失败</div>
              </div>
              <div style={{ color: '#ccc' }}>签约后及时刷新该页面</div>
              {/* <Button
            onClick={() => {
              doAction(ECARINSURANCE_OPERATE_ACTION.DELETE_DRAFT);
            }}
          >
            测试签约
          </Button> */}
            </>
          )}
        </div>
      ),
      okButtonProps: {
        style: { display: 'none' },
      },
    });
    return;
  }

  /**
   * 上传首付凭证
   */
  function handleUploadDownPaymentsVoucher() {
    paymentInfoForm?.resetFields();
    modal.confirm({
      title: '上传首付凭证',
      icon: null,
      closable: true,
      content: (
        <Form form={paymentInfoForm} layout="vertical">
          <Form.Item name="paymentDocumentUrlList" label="汇款凭证" rules={[{ required: true }]}>
            <UploadMultipleImg form={paymentInfoForm} />
          </Form.Item>
          <Form.Item name="paymentDate" label="汇款时间" rules={[{ required: true }]}>
            <DatePicker showTime />
          </Form.Item>
          <Form.Item name="paymentDocumentOssUrlList" style={{ display: 'none' }} />
        </Form>
      ),
      async onOk() {
        await paymentInfoForm.validateFields();
        doAction(ECARINSURANCE_OPERATE_ACTION.UPLOAD_DOWN_PAYMENTS_VOUCHER);
      },
      okText: '确认上传',
    });
  }

  // 校验车辆是否都已经检查
  function verifyCarIsCheck(): boolean {
    const notCheckCar = carInfo.filter((item) => !item.inspected);
    if (notCheckCar?.length) {
      Modal.warning({
        title: '未检查车辆信息',
        okText: '好的',

        content: (
          <div>
            {notCheckCar.map((item) => {
              return <Tag color="error">{item.plateNo}</Tag>;
            })}
            车辆未检查
          </div>
        ),
      });
      return false;
    } else {
      // 说明车辆都已经全部检查完毕
      return true;
    }
  }
  // 审核通过 需要二次弹窗确认
  // function handleSubmitPass() {
  //   // 先校验车辆是否都已检查
  //   if (verifyCarIsCheck()) {
  //     Modal.confirm({
  //       icon: null,
  //       content: '您确认要通过该订单吗?',
  //       okText: '确认',
  //       cancelText: '取消',
  //       onOk() {
  //         doAction(ECARINSURANCE_OPERATE_ACTION.SUBMIT_PASS);
  //       },
  //     });
  //   }
  // }
  // // 驳回
  // function handleReject() {
  //   if (verifyCarIsCheck()) {
  //     Modal.confirm({
  //       title: '驳回订单',
  //       content: (
  //         <TextArea
  //           rows={4}
  //           onChange={(e) => {
  //             refData.current.rejectMsg = e.target.value;
  //           }}
  //         />
  //       ),
  //       okText: '确认驳回',
  //       cancelText: '取消',
  //       okType: 'danger',
  //       onOk() {
  //         doAction(ECARINSURANCE_OPERATE_ACTION.SUBMIT_FOR_REVIEW_REJECT);
  //       },
  //     });
  //   }
  // }

  /**
   * 删除草稿
   */
  function handleDeleteDraft() {
    if (!orderNo) {
      return message.error('订单号不存在无法删除');
    } else {
      Modal.confirm({
        title: '删除草稿',
        content: '您确认要删除该草稿订单吗?',
        async onOk() {
          doAction(ECARINSURANCE_OPERATE_ACTION.DELETE_DRAFT);
        },
      });
    }
    return;
  }
  function handleUploadDownCheckPass() {
    Modal.confirm({
      content: '您确认要通过该审核吗?',
      onOk() {
        doAction(ECARINSURANCE_OPERATE_ACTION.UPLOAD_DOWN_CHECK_PASS);
      },
    });
  }
  function handleDownPaymentsReject() {
    Modal.confirm({
      content: '您确认要驳回该订单吗?',
      onOk() {
        doAction(ECARINSURANCE_OPERATE_ACTION.DOWN_PAYMENTS_REJECT);
      },
    });
  }
  const handleSubmitForReview = (callback: () => void) => {
    Modal.confirm({
      title: '是否继续提交审核',
      closable: false,
      maskClosable: false,
      closeIcon: null,
      okText: '继续提交',
      onOk() {
        callback();
      },
    });
  };
  const handleWithdraw = () => {
    Modal.confirm({
      content: '您确认要撤回该订单吗?',
      async onOk() {
        const humpOperate = underlineToHump('WITHDRAW');
        try {
          setbtnLoading({ [humpOperate]: true });
          const data = await cancelClaim({ orderNo });
          message.success('操作成功');
          await getDetail(data);
        } catch (error) {
          return Promise.reject(error);
        } finally {
          setbtnLoading({ [humpOperate]: false });
        }
      },
    });
  };
  // 提交or审核二次检查车辆重复进件
  function handleCheckRepeatOrder(type: UcarInsuranceOperate, callback: () => void) {
    let okText = '确认';
    switch (type) {
      case ECARINSURANCE_OPERATE_ACTION.SUBMIT_FOR_REVIEW:
        okText = '继续提交';
        break;
      case ECARINSURANCE_OPERATE_ACTION.SUBMIT_PASS:
        okText = '审核通过';
        break;
      default:
        break;
    }
    // 检查车辆重复进件
    updateCarInspected({
      orderNo,
      vinList: carInfo.filter((item) => !item.deleteFlag).map((item) => item.vin), // 已被标记的车辆不应该被校验
      type: 2, // 检查车辆进件信息
    }).then((res) => {
      // 表示存在重复进件订单
      const vinOrderListMap = res?.data?.vinOrderListMap;
      if (!!vinOrderListMap && Object.keys(vinOrderListMap)?.length) {
        Modal.confirm({
          title: '下方车辆当前有在途订单',
          width: 800,
          content: (
            <>
              {Object.keys(vinOrderListMap).map((key) => (
                <div key={key}>
                  <span>车架号：{key}</span>，
                  <span>
                    在途订单号：
                    {vinOrderListMap[key].map((orderInfo, index) => (
                      <>
                        <a
                          key={orderInfo.orderNo}
                          style={
                            !orderInfo?.hasChannelPrivilege
                              ? { cursor: 'not-allowed', color: '#ccc' }
                              : undefined
                          }
                          onClick={(e) => {
                            if (!orderInfo?.hasChannelPrivilege) {
                              e.preventDefault();
                              e.stopPropagation();
                              return;
                            }
                          }}
                          target="_blank"
                          href={`/businessMng/car-insurance/detail?orderNo=${orderInfo.orderNo}`}
                          rel="noreferrer"
                        >
                          {orderInfo.orderNo}
                        </a>
                        {index < vinOrderListMap[key].length - 1 ? '， ' : ''}
                      </>
                    ))}
                  </span>
                </div>
              ))}
            </>
          ),
          closable: false,
          maskClosable: false,
          closeIcon: null,
          okText,
          onOk() {
            callback();
          },
        });
      } else {
        callback();
      }
    });
  }
  // 提交or审核二次确认逾期信息弹窗
  function handleOverdueConfirm(type: UcarInsuranceOperate, callback: () => void) {
    let okText = '确认';
    switch (type) {
      case ECARINSURANCE_OPERATE_ACTION.SUBMIT_FOR_REVIEW:
        okText = '继续提交';
        break;
      case ECARINSURANCE_OPERATE_ACTION.SUBMIT_PASS:
        okText = '审核通过';
        break;
      default:
        break;
    }
    const getParams = () => {
      const enterpriseInfo = enterpriseInfoForm.getFieldsValue();
      const personalInfo = loanPersonalInfoForm.getFieldsValue();
      const params = !!enterpriseInfo?.epAuthNo
        ? {
            idNo: enterpriseInfo?.epAuthNo,
            type: 0, // 查企业
          }
        : {
            idNo: personalInfo?.idNo,
            type: 1, // 查个人
          };
      return {
        idNo: params.idNo,
        type: params.type as 0 | 1,
      };
    };
    // 校验逾期信息
    getOverdueCaseInfo(getParams()).then((res) => {
      // 表示存在逾期订单
      if (res?.data?.hasOverdueCase) {
        Modal.confirm({
          title: '客户当前有逾期未还',
          closable: false,
          maskClosable: false,
          closeIcon: null,
          content: `最大逾期天数: ${res?.data?.overdueCase?.overdueLongestDays ?? 0}天`,
          okText,
          onOk() {
            callback();
          },
        });
      } else {
        callback();
      }
    });
  }

  function operate(type: UcarInsuranceOperate) {
    switch (type) {
      case ECARINSURANCE_OPERATE_ACTION.CLOSED: // 1 关闭
        handleClosed();
        break;
      case ECARINSURANCE_OPERATE_ACTION.DELETE_DRAFT: // 2删除草稿
        handleDeleteDraft();
        break;
      case ECARINSURANCE_OPERATE_ACTION.SAVE_DRAFT: // 3保存草稿
        doAction(ECARINSURANCE_OPERATE_ACTION.SAVE_DRAFT);
        break;
      case ECARINSURANCE_OPERATE_ACTION.MONTHLY_CALCU: // 4月供测算
        handleMonthlyCalcu();
        break;
      case ECARINSURANCE_OPERATE_ACTION.SUBMIT_FOR_REVIEW: // 5 提交审核
        // 逾期&重复进件二次确认
        handleSubmitForReview(() => handleCheckRepeatOrder(type, handleSunmitForReview));
        // handleOverdueConfirm(type, () => handleCheckRepeatOrder(type, handleSunmitForReview));
        break;
      case ECARINSURANCE_OPERATE_ACTION.WITHDRAW_APPLICATION: // 6取消申请
        doAction(ECARINSURANCE_OPERATE_ACTION.WITHDRAW_APPLICATION);
        break;
      case ECARINSURANCE_OPERATE_ACTION.SIGN_QR_CODE: // 7 签约二维码
      case ECARINSURANCE_OPERATE_ACTION.DOWN_PAYMENTS_QR_CODE: // 7.1 首付二维码
        handleSignOrDownPaymentsQrCode(type);
        break;
      case ECARINSURANCE_OPERATE_ACTION.UPLOAD_DOWN_PAYMENTS_VOUCHER: // 上8传首付凭证
        handleUploadDownPaymentsVoucher();
        break;
      // case ECARINSURANCE_OPERATE_ACTION.SUBMIT_FOR_REVIEW_REJECT: // 9 审核驳回
      //   handleReject();
      //   break;
      // case ECARINSURANCE_OPERATE_ACTION.SUBMIT_PASS: // 10 审核通过
      //   // 逾期&重复进件二次确认
      //   handleOverdueConfirm(type, () => handleCheckRepeatOrder(type, handleSubmitPass));
      //   break;
      case ECARINSURANCE_OPERATE_ACTION.UPLOAD_DOWN_CHECK_PASS: // 11 首付审核审核通过
        handleUploadDownCheckPass();
        break;
      case ECARINSURANCE_OPERATE_ACTION.DOWN_PAYMENTS_REJECT: // 12 首付审核审核驳回
        handleDownPaymentsReject();
        break;
      case 'WITHDRAW': // 撤回
        handleWithdraw();
        break;
      default:
    }
  }

  function getOperateMap() {
    const operateMap = {
      PERSONAL: { ...OperateMap, ...PersonalOperateMap },
      COMPANY: { ...OperateMap, ...CompanyOperateMap },
    };
    const channelOperate = {
      PERSONAL: { ...ChannelUserOperateMap, ...PersonalChannelUserOperateMap },
      COMPANY: { ...ChannelUserOperateMap, ...CompanyChannelUserOperateMap },
    };
    return channelCode ? channelOperate[borrowerType || ''] : operateMap[borrowerType || ''];
  }

  /**
   * 如果是渠道用户
   * 不同的状态可以进行不同的操作
   * 草稿状态 - 关闭 删除草稿 月供测算 提交审核 保存草稿
   */
  function getExtraOperate() {
    const operateMap = getOperateMap();
    // 订单状态为 待放款 且 放款方式为线下 时，需要允许取消，且需要二次确认。todo: 待补充权限禁用
    const allowOfflineLoanAction =
      detailData?.lendingModel === LENDING_MODEL_ENUM.OFFLINE &&
      access?.hasAccess('cancel_apply_detail_car_insurance_list');
    console.log('allowOfflineLoanAction', allowOfflineLoanAction);
    // 自定义订单按钮禁用逻辑
    const disableMap = {};
    // 自定义订单按钮展示逻辑
    const showBtnMap = {
      [EcarInsuranceStatus.LOAN_PENDING]: {
        WITHDRAW_APPLICATION: allowOfflineLoanAction,
      },
    };
    // 自定义订单按钮二次确认逻辑
    const confirmMap = {
      [EcarInsuranceStatus.LOAN_PENDING]: {
        WITHDRAW_APPLICATION: allowOfflineLoanAction,
      },
    };

    const operates = operateMap?.[EcarInsuranceStatus?.[detailData?.orderStatus || 0]] ?? [];
    return operates.map((item: UcarInsuranceOperate) => {
      const humpOperate = underlineToHump(item);
      return (
        <ConfirmButton
          key={item}
          // 需要二次确认的操作
          needConfirm={confirmMap?.[detailData?.orderStatus]?.[item] ?? false}
          // 有些按钮不需要loading 永远都是false
          loading={btnLoading[humpOperate]}
          // 只要是审核驳回 和 取消申请 都是红色的
          danger={[
            ECARINSURANCE_OPERATE_ACTION.SUBMIT_FOR_REVIEW_REJECT,
            ECARINSURANCE_OPERATE_ACTION.WITHDRAW_APPLICATION,
          ].includes(item as ECARINSURANCE_OPERATE_ACTION)}
          // 处于待审核状态且为非个人领取的订单 不能做任何操作，只保留关闭按钮操作
          disable={disableMap?.[detailData?.orderStatus]?.[item] || false}
          show={showBtnMap?.[detailData?.orderStatus]?.[item]}
          buttonText={EcarInsuranceOperate[item]}
          title={EcarInsuranceOperate[item]}
          onConfirm={() => {
            operate(item);
          }}
        />
      );
    });
  }
  const handleBeforeSubmit = async (values: {
    userBehaviorEnum: string;
    rejectMsg?: string;
    remarkMsg?: string;
  }) => {
    refData.current.rejectMsg = values.rejectMsg;
    refData.current.remarkMsg = values.remarkMsg;
    if (values.userBehaviorEnum === ECARINSURANCE_OPERATE_ACTION.SUBMIT_FOR_REVIEW_REJECT) {
      await doAction(ECARINSURANCE_OPERATE_ACTION.SUBMIT_FOR_REVIEW_REJECT);
    } else if (values.userBehaviorEnum === ECARINSURANCE_OPERATE_ACTION.SUBMIT_PASS) {
      return new Promise<void>((r, j) => {
        handleCheckRepeatOrder(ECARINSURANCE_OPERATE_ACTION.SUBMIT_PASS, () => {
          if (verifyCarIsCheck()) {
            Modal.confirm({
              icon: null,
              content: '您确认要通过该订单吗?',
              okText: '确认',
              cancelText: '取消',
              onOk: async () => {
                await doAction(ECARINSURANCE_OPERATE_ACTION.SUBMIT_PASS);
                r();
              },
            });
          }
        });
      });
    }
  };
  return (
    <div className="car-insurance-edit">
      <HeaderTab />
      <PageContainer
        header={{
          title: '订单详情(车险分期)',
          breadcrumb: {},
          extra: getExtraOperate(),
        }}
      >
        <Spin spinning={detailLoading}>
          <Card>
            {/* 驳回原因 & 订单状态+订单号展示 */}
            <ApproveInfo />
            <Divider />
            {detailData.orderStatus === EcarInsuranceStatus.TO_BE_DOWN_PAYMENT_AUDIT ? (
              <>
                <DownPaymentsVoucherInfo />
                <Divider />
              </>
            ) : null}
            {/* 借款信息 审核通过即以后才会展示借款信息*/}
            {detailData?.orderStatus >= EcarInsuranceStatus.SIGN && (
              <>
                <LoanInfo />
                <Divider />
              </>
            )}
            {/* 基本信息 */}
            <BaseInfo
              baseInfoForm={baseInfoForm}
              productInfoForm={productInfoForm}
              enterpriseInfoForm={enterpriseInfoForm}
              companyInfoForm={companyInfoForm}
              refData={refData.current}
            />
            {/* 只有选了借款人类型才展示 */}
            {borrowerType ? (
              <>
                <Divider />
                {/* 产品信息 */}
                <ProductInfo
                  baseInfoForm={baseInfoForm}
                  productInfoForm={productInfoForm}
                  refData={refData.current}
                  enterpriseInfoForm={enterpriseInfoForm}
                  key={borrowerType}
                />
                <Divider />
                {/* 企业信息 */} {/* 个人借款信息 */}
                {borrowerType === 'COMPANY' || !borrowerType ? (
                  <EnterpriseInfo enterpriseInfoForm={enterpriseInfoForm} />
                ) : (
                  <LoanPersonal loanPersonalInfoForm={loanPersonalInfoForm} />
                )}
                <Divider />
                {/* 车辆信息 */}
                <CarInsuranceBulkOcrProvider>
                  <CarInfo
                    updateDraft={() =>
                      doAction(ECARINSURANCE_OPERATE_ACTION.SAVE_DRAFT, { noLoading: true })
                    }
                    getOcrResult={setOcrMatchResult}
                  />
                </CarInsuranceBulkOcrProvider>
                <Divider />
                {/* 保险信息 */}
                <CompanyInfo companyInfoForm={companyInfoForm} />
                <Divider />
                {/* 40 51 60、70、80、81、-1、72 */}
                {[
                  EcarInsuranceStatus.REPAYING,
                  EcarInsuranceStatus.LOAN_PENDING,
                  EcarInsuranceStatus.OVERDUE,
                  EcarInsuranceStatus.SETTLE,
                  EcarInsuranceStatus.SURRENDERED,
                  EcarInsuranceStatus.CANCELED,
                  EcarInsuranceStatus.INVALIDATION,
                  EcarInsuranceStatus.EARLY_SETTLE,
                ].includes(detailData.orderStatus) ? (
                  <>
                    <DownPaymentsVoucherInfo />
                    <Divider />
                  </>
                ) : null}
                {/* 其他资料 */}
                <OtherInfo otherInfoForm={otherInfoForm} />
                {/* 后补材料 */}
                <AppendInfo />
                {/* 备注 */}
                <Remark remarkForm={remarkForm} />
                {/* 操作记录 */}
                <Divider />
                <OperationRecords />
                <div>{contextHolder}</div>
              </>
            ) : null}
          </Card>
        </Spin>
      </PageContainer>
      <ApproveCard beforeSubmit={handleBeforeSubmit} />
    </div>
  );
}

export default memo(CarInsuranceEdit);
